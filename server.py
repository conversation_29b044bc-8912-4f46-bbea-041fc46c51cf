import os ,json
from datetime import datetime, timezone
from loguru import logger
from pydantic_ai.exceptions import UnexpectedModelBehavior
import uuid
from fastapi import FastAPI, Request, Depends
from contextlib import asynccontextmanager
from pydantic import BaseModel
from typing import  Optional, Dict, Any, Literal
import uvicorn
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from pydantic_ai import Agent, UserPromptNode, ModelRequestNode, CallToolsNode
from starlette.responses import StreamingResponse
from typing_extensions import TypedDict

from db.repository import Database
from pydantic_ai.messages import (
    ModelMessage,
    ModelRequest,
    ModelResponse,
    TextPart,
    UserPromptPart,
)
from pydantic_graph import End

from devtron_agent_config import get_devtron_agent_config
from models import ChatRequest
from prompt.tmplate import TemplateRenderer
from tool import AgentDep
from vector_store import VectorStoreManager

THIS_DIR = Path(__file__).parent

@asynccontextmanager
async def lifespan(app: FastAPI):
    async with Database.connect() as db:
        agent = await get_devtron_agent_config()
        async with agent.run_mcp_servers():
            app.state.db = db
            app.state.agent = agent
            app.state.templateRenderer = TemplateRenderer()
            yield {'db': db, 'agent': agent, 'templateRenderer': TemplateRenderer}


# async def get_agent() -> Agent:
#     return devtron_agent
#

app = FastAPI(lifespan=lifespan)

@app.get('/')
async def root():
    return {"message": "Welcome to the API"}


class ChatMessage(TypedDict):
    """Format of messages sent to the browser."""
    role: Literal['user', 'model', 'system']
    timestamp: str
    content: str


async def get_db(request: Request) -> Database:
    return request.state.db


async def get_agent(request: Request) -> Agent:
    return request.app.state.agent

async def get_template_renderer(request: Request) -> TemplateRenderer:
    return request.app.state.templateRenderer

@app.post("/chat")
async def chat(
        request: ChatRequest,
        db: Database = Depends(get_db),
        agent: Agent = Depends(get_agent),
        template_renderer: TemplateRenderer = Depends(get_template_renderer)

) -> StreamingResponse:
    async def stream_messages():
        logger.info("CHAT_REQUEST:{}",request)
        if not request.session_id:
            # if no session id is provided, create a new one
            request.session_id = str(uuid.uuid4())
            yield json.dumps(
                {
                    'role': 'system',
                    'timestamp': datetime.now(tz=timezone.utc).isoformat(),
                    'type': 'PROGRESS',
                    'content': '{"sessionId": "' f'{request.session_id}' '"}'
                }).encode('utf-8')+ b'\n'
        """Streams new line delimited JSON `Message`s to the client."""
        # stream the user prompt so that can be displayed straight away
        yield json.dumps(
            {
                'role': 'user',
                'timestamp': datetime.now(tz=timezone.utc).isoformat(),
                'content': request.messages,
                'type': 'PROGRESS'
            }).encode('utf-8')+ b'\n'
        messages = await db.get_messages(session_id=request.session_id)
        logger.debug("old messages :{}",messages)
        msg=template_renderer.render(template_path="./prompt/chat_request.j2", chat_request=request)

        itr = agent.iter(
            user_prompt=msg,
            message_history=messages,
            deps=AgentDep(session_id=request.session_id),
           )

        async with  itr as agent_run:
            async for node in agent_run:
                if isinstance(node, UserPromptNode):
                    # print(node.user_prompt)
                    continue
                elif isinstance(node, ModelRequestNode):
                    continue
                    # print(node.request.parts)
                elif isinstance(node, CallToolsNode):
                    for part in node.model_response.parts:
                        if isinstance(part, TextPart):
                            try:
                                j={
                                    'role': 'model',
                                    'timestamp': datetime.now(tz=timezone.utc).isoformat(),
                                    'content': part.content,
                                    'type': 'PROGRESS'
                                }
                                json_msg = json.dumps(j).encode('utf-8')
                                yield json_msg + b'\n'
                            except Exception  as e:
                                error_msg = {
                                    'role': 'model',
                                    'timestamp': datetime.now(tz=timezone.utc).isoformat(),
                                    'content': f'Error processing message: {str(e)}'
                                }
                                logger.error("ERROR_MSG",error_msg)
                elif isinstance(node, End):
                    j = {
                        'role': 'model',
                        'timestamp': datetime.now(tz=timezone.utc).isoformat(),
                        'content': node.data.output,
                        'type': 'END'
                    }
                    json_msg = json.dumps(j).encode('utf-8')
                    yield json_msg + b'\n'
        history = agent_run.result.new_messages_json()
        await db.add_messages(messages=history, session_id=request.session_id)
        logger.info("req token: {}, res token {}, total req{}",
                    agent_run.ctx.state.usage.request_tokens,
                    agent_run.ctx.state.usage.response_tokens,
                    agent_run.ctx.state.usage.requests)

    return StreamingResponse(stream_messages(), media_type='text/plain')


if __name__ == "__main__":
    logger.info("starting server")
    VectorStoreManager.get_vector_store_with_embeddings()
    uvicorn.run(
        "server:app",
        host=os.getenv("HOST", "0.0.0.0"),
        port=int(os.getenv("PORT", "8000")),
        reload=os.getenv("UVICORN_RELOAD", "FALSE").upper() == "TRUE",
        workers=int(os.getenv("WORKERS", "1"),),
        log_level=os.getenv("LOG_LEVEL", "debug"),
    )

class ChatMessage(TypedDict):
    """Format of messages sent to the browser."""

    role: Literal['user', 'model','system']
    timestamp: str
    content: str


def to_chat_message(m: ModelMessage) -> ChatMessage:
    first_part = m.parts[0]
    if isinstance(m, ModelRequest):
        if isinstance(first_part, UserPromptPart):
            assert isinstance(first_part.content, str)
            return {
                'role': 'user',
                'timestamp': first_part.timestamp.isoformat(),
                'content': first_part.content,
            }
    elif isinstance(m, ModelResponse):
        if isinstance(first_part, TextPart):
            return {
                'role': 'model',
                'timestamp': m.timestamp.isoformat(),
                'content': first_part.content,
            }
    raise UnexpectedModelBehavior(f'Unexpected message type for chat app: {m}')
