# LangGraph Implementation Quick Start Guide

## Overview
This guide provides a quick reference for implementing the LangGraph-based Devtron agent workflow system.

## Prerequisites
- Python 3.9+
- Existing Devtron agent codebase
- LangGraph library
- Redis for state persistence

## Quick Setup

### 1. Install Dependencies
```bash
pip install langgraph langchain-core redis
```

### 2. Project Structure
```
workflows/
├── __init__.py
├── state.py              # AgentState definition
├── nodes/                # Individual workflow nodes
│   ├── __init__.py
│   ├── base.py          # BaseAgentNode
│   ├── classification.py # Query classification nodes
│   ├── tools.py         # Tool execution nodes
│   └── validation.py    # Result validation nodes
├── graphs/              # Workflow graph definitions
│   ├── __init__.py
│   ├── general_knowledge.py
│   ├── debugging.py
│   └── devtron_features.py
├── fallback/            # Fallback management
│   ├── __init__.py
│   ├── strategies.py
│   └── manager.py
└── utils/               # Utilities
    ├── __init__.py
    ├── graph_builder.py
    └── metrics.py
```

## Core Implementation Steps

### Step 1: Define Agent State
```python
# workflows/state.py
from dataclasses import dataclass
from typing import Dict, List, Any, Optional
from enum import Enum

class QueryType(Enum):
    GENERAL_KNOWLEDGE = "general_knowledge"
    SPECIFIC_DEBUGGING = "specific_debugging"
    DEVTRON_FEATURES = "devtron_features"
    COMPLEX_TROUBLESHOOTING = "complex_troubleshooting"

@dataclass
class AgentState:
    # Input
    query: str
    session_id: str
    
    # Classification
    query_type: Optional[QueryType] = None
    confidence: float = 0.0
    
    # Execution
    current_node: str = "start"
    execution_path: List[str] = None
    tool_results: List[Dict] = None
    
    # Output
    final_response: Optional[str] = None
    
    def __post_init__(self):
        if self.execution_path is None:
            self.execution_path = []
        if self.tool_results is None:
            self.tool_results = []
```

### Step 2: Create Base Node
```python
# workflows/nodes/base.py
from abc import ABC, abstractmethod
from typing import Dict, Any
from workflows.state import AgentState

class BaseAgentNode(ABC):
    def __init__(self, name: str):
        self.name = name
    
    @abstractmethod
    async def execute(self, state: AgentState) -> AgentState:
        """Execute the node logic and return updated state"""
        pass
    
    def validate_input(self, state: AgentState) -> bool:
        """Validate input state before execution"""
        return True
    
    def validate_output(self, state: AgentState) -> bool:
        """Validate output state after execution"""
        return True
    
    async def run(self, state: AgentState) -> AgentState:
        """Main execution wrapper with validation"""
        if not self.validate_input(state):
            raise ValueError(f"Invalid input for node {self.name}")
        
        # Add to execution path
        state.execution_path.append(self.name)
        state.current_node = self.name
        
        # Execute node logic
        updated_state = await self.execute(state)
        
        if not self.validate_output(updated_state):
            raise ValueError(f"Invalid output from node {self.name}")
        
        return updated_state
```

### Step 3: Implement Classification Node
```python
# workflows/nodes/classification.py
from workflows.nodes.base import BaseAgentNode
from workflows.state import AgentState, QueryType
from query_classifier import QueryClassifier

class QueryClassifierNode(BaseAgentNode):
    def __init__(self):
        super().__init__("query_classifier")
        self.classifier = QueryClassifier()
    
    async def execute(self, state: AgentState) -> AgentState:
        # Classify the query
        query_type = self.classifier.classify_query(state.query)
        confidence = self._calculate_confidence(state.query, query_type)
        
        # Update state
        state.query_type = query_type
        state.confidence = confidence
        
        return state
    
    def _calculate_confidence(self, query: str, query_type: QueryType) -> float:
        # Implementation from existing classifier
        # Return confidence score 0.0 - 1.0
        pass
```

### Step 4: Create Simple Workflow Graph
```python
# workflows/graphs/general_knowledge.py
from langgraph.graph import StateGraph, END
from workflows.state import AgentState
from workflows.nodes.classification import QueryClassifierNode
from workflows.nodes.llm import DirectLLMNode
from workflows.nodes.validation import ResponseValidatorNode

def create_general_knowledge_graph():
    # Create the graph
    workflow = StateGraph(AgentState)
    
    # Add nodes
    workflow.add_node("classify", QueryClassifierNode())
    workflow.add_node("llm_response", DirectLLMNode())
    workflow.add_node("validate", ResponseValidatorNode())
    
    # Add edges
    workflow.add_edge("classify", "llm_response")
    workflow.add_edge("llm_response", "validate")
    workflow.add_edge("validate", END)
    
    # Set entry point
    workflow.set_entry_point("classify")
    
    return workflow.compile()
```

### Step 5: Implement Conditional Routing
```python
# workflows/graphs/debugging.py
from langgraph.graph import StateGraph, END
from workflows.state import AgentState

def route_after_health_check(state: AgentState) -> str:
    """Route based on health check results"""
    if not state.tool_results:
        return "fallback"
    
    last_result = state.tool_results[-1]
    if "unhealthy" in last_result.get("status", "").lower():
        return "get_logs"
    elif "pending" in last_result.get("status", "").lower():
        return "get_deployment_status"
    else:
        return "synthesize_response"

def create_debugging_graph():
    workflow = StateGraph(AgentState)
    
    # Add nodes
    workflow.add_node("classify", QueryClassifierNode())
    workflow.add_node("health_check", AppHealthCheckNode())
    workflow.add_node("get_logs", LogAnalysisNode())
    workflow.add_node("get_deployment_status", DeploymentStatusNode())
    workflow.add_node("synthesize_response", ResponseSynthesizerNode())
    workflow.add_node("fallback", FallbackNode())
    
    # Add conditional edges
    workflow.add_edge("classify", "health_check")
    workflow.add_conditional_edges(
        "health_check",
        route_after_health_check,
        {
            "get_logs": "get_logs",
            "get_deployment_status": "get_deployment_status",
            "synthesize_response": "synthesize_response",
            "fallback": "fallback"
        }
    )
    
    workflow.add_edge("get_logs", "synthesize_response")
    workflow.add_edge("get_deployment_status", "synthesize_response")
    workflow.add_edge("synthesize_response", END)
    workflow.add_edge("fallback", END)
    
    workflow.set_entry_point("classify")
    
    return workflow.compile()
```

### Step 6: Main Execution Engine
```python
# workflows/executor.py
from workflows.graphs.general_knowledge import create_general_knowledge_graph
from workflows.graphs.debugging import create_debugging_graph
from workflows.state import AgentState, QueryType

class WorkflowExecutor:
    def __init__(self):
        self.graphs = {
            QueryType.GENERAL_KNOWLEDGE: create_general_knowledge_graph(),
            QueryType.SPECIFIC_DEBUGGING: create_debugging_graph(),
            # Add other workflow graphs
        }
    
    async def execute(self, query: str, session_id: str) -> str:
        # Initialize state
        initial_state = AgentState(
            query=query,
            session_id=session_id
        )
        
        # Quick classification to select graph
        # (You might want to do this within the graph instead)
        classifier = QueryClassifierNode()
        classified_state = await classifier.run(initial_state)
        
        # Select appropriate graph
        graph = self.graphs.get(
            classified_state.query_type,
            self.graphs[QueryType.GENERAL_KNOWLEDGE]  # Default
        )
        
        # Execute workflow
        final_state = await graph.ainvoke(classified_state)
        
        return final_state.final_response
```

## Integration with Existing System

### 1. Update Agent Configuration
```python
# devtron_agent_config.py
from workflows.executor import WorkflowExecutor

class DevtronAgentConfig:
    def __init__(self):
        # ... existing code ...
        self.workflow_executor = WorkflowExecutor()
    
    async def get_langgraph_agent(self):
        return self.workflow_executor
```

### 2. Add Migration Support
```python
# migration/ab_testing.py
import random

class ABTestingFramework:
    def __init__(self, langgraph_percentage: float = 0.1):
        self.langgraph_percentage = langgraph_percentage
    
    def should_use_langgraph(self, session_id: str) -> bool:
        # Deterministic based on session for consistency
        hash_value = hash(session_id) % 100
        return hash_value < (self.langgraph_percentage * 100)
    
    async def route_query(self, query: str, session_id: str):
        if self.should_use_langgraph(session_id):
            # Use LangGraph workflow
            executor = WorkflowExecutor()
            return await executor.execute(query, session_id)
        else:
            # Use existing smart agent
            # ... existing agent logic ...
            pass
```

## Testing Strategy

### Unit Tests
```python
# tests/test_nodes.py
import pytest
from workflows.nodes.classification import QueryClassifierNode
from workflows.state import AgentState, QueryType

@pytest.mark.asyncio
async def test_query_classifier():
    node = QueryClassifierNode()
    state = AgentState(
        query="What is Kubernetes?",
        session_id="test"
    )
    
    result = await node.run(state)
    
    assert result.query_type == QueryType.GENERAL_KNOWLEDGE
    assert result.confidence > 0.5
    assert "query_classifier" in result.execution_path
```

### Integration Tests
```python
# tests/test_workflows.py
import pytest
from workflows.graphs.general_knowledge import create_general_knowledge_graph
from workflows.state import AgentState

@pytest.mark.asyncio
async def test_general_knowledge_workflow():
    graph = create_general_knowledge_graph()
    
    initial_state = AgentState(
        query="What is a Kubernetes pod?",
        session_id="test"
    )
    
    final_state = await graph.ainvoke(initial_state)
    
    assert final_state.final_response is not None
    assert len(final_state.execution_path) > 0
    assert final_state.query_type == QueryType.GENERAL_KNOWLEDGE
```

## Monitoring & Debugging

### Add Execution Tracing
```python
# workflows/utils/tracing.py
import time
from typing import Dict, Any

class ExecutionTracer:
    def __init__(self):
        self.traces = {}
    
    def start_execution(self, session_id: str, query: str):
        self.traces[session_id] = {
            "query": query,
            "start_time": time.time(),
            "nodes": [],
            "errors": []
        }
    
    def log_node_execution(self, session_id: str, node_name: str, duration: float):
        if session_id in self.traces:
            self.traces[session_id]["nodes"].append({
                "name": node_name,
                "duration": duration,
                "timestamp": time.time()
            })
    
    def get_trace(self, session_id: str) -> Dict[str, Any]:
        return self.traces.get(session_id, {})
```

## Performance Optimization Tips

1. **Parallel Execution**: Use LangGraph's parallel execution for independent tools
2. **Caching**: Cache tool results and LLM responses
3. **Streaming**: Stream responses for long-running workflows
4. **Resource Pooling**: Pool database connections and HTTP clients
5. **Monitoring**: Track node execution times and optimize bottlenecks

## Common Patterns

### Fallback Pattern
```python
def create_fallback_edge(primary_node: str, fallback_node: str):
    def route_with_fallback(state: AgentState) -> str:
        if state.tool_results and state.tool_results[-1].get("error"):
            return fallback_node
        return "continue"
    
    return route_with_fallback
```

### Retry Pattern
```python
class RetryNode(BaseAgentNode):
    def __init__(self, target_node: BaseAgentNode, max_retries: int = 3):
        super().__init__(f"retry_{target_node.name}")
        self.target_node = target_node
        self.max_retries = max_retries
    
    async def execute(self, state: AgentState) -> AgentState:
        for attempt in range(self.max_retries):
            try:
                return await self.target_node.run(state)
            except Exception as e:
                if attempt == self.max_retries - 1:
                    raise
                # Log retry attempt
                continue
```

This quick start guide provides the foundation for implementing LangGraph workflows. Start with simple workflows and gradually add complexity as you become more familiar with the patterns.
