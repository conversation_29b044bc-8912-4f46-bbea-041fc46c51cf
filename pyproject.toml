[tool.poetry]
name = "devtron-agent"
version = "0.0.1"
description = "AI-powered assistant for debugging Kubernetes applications and Devtron platform"
authors = ["nishant <<EMAIL>>"]
license = "devtron enterprise"
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.10,<4.0"
pydantic-ai = ">=0.2.4,<0.3.0"
langchain-postgres = ">=0.0.13,<0.0.15"
greenlet = ">=2.0.2,<3.0.0"
fastapi = {extras = ["standard"], version = ">=0.115.12,<0.116.0"}
python-multipart = ">=0.0.20,<0.0.21"
uvicorn = ">=0.29.0,<0.30.0"
loguru = ">=0.7.3,<0.8.0"
fastmcp = ">=2.4.0,<3.0.0"
deepmerge = ">=2.0,<3.0"
fastembed = ">=0.7.0,<0.8.0"
unstructured = {extras = ["md"], version = ">=0.17.2,<0.18.0"}
psycopg2-binary = ">=2.9.10,<3.0.0"
langchain-community = ">=0.3.24,<0.4.0"
pydantic-ai-slim = {extras = ["duckduckgo"], version = ">=0.2.18,<0.3.0"}
# LangGraph dependencies
langgraph = ">=0.2.50,<0.3.0"
langchain-core = ">=0.3.24,<0.4.0"
langchain-openai = ">=0.2.14,<0.3.0"
langchain-anthropic = ">=0.2.7,<0.3.0"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
[tool.poetry.group.dev.dependencies]
deptry = "^0.23.0"


