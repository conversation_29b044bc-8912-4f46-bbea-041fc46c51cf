from loguru import logger
from devtron_util import devtron_get_request_with_auth, devtron_post_request_with_auth, K8sData, LogRequest
from embedding import search_vector_index
from pydantic_ai.tools import RunContext
from dataclasses import dataclass
from query_classifier import QueryClassifier, QueryType
from intelligent_tool_router import IntelligentToolRouter
from typing import Optional


@dataclass
class AgentDep:
    session_id: str = None
    query_context: Optional[str] = None  # Store original query for context


class SmartDevtronTools:
    """Enhanced tools with intelligent routing and caching"""
    
    def __init__(self):
        self.classifier = QueryClassifier()
        self.router = IntelligentToolRouter()
        self._doc_cache = {}  # Simple cache for documentation searches
    
    @staticmethod
    def get_app_detail(ctx: RunContext[AgentDep], application_id: int, environment_id: int) -> str:
        """Get application details - prioritized for debugging queries"""
        logger.info("get app detail for session:{}, app_id:{}, env_id:{}", 
                   ctx.deps.session_id, application_id, environment_id)
        uri = "app/detail/resource-tree?app-id={}&env-id={}".format(application_id, environment_id)
        return devtron_get_request_with_auth({}, uri)

    @staticmethod
    def get_deployment_status(ctx: RunContext[AgentDep], application_id: int, environment_id: int) -> str:
        """Get deployment status - useful for deployment-related queries"""
        logger.info("get deployment status session:{}, app_id:{}, env_id:{}", 
                   ctx.deps.session_id, application_id, environment_id)
        uri = "app/deployment-status/timeline/{}/{}?showTimeline=true".format(application_id, environment_id)
        return devtron_get_request_with_auth({}, uri)

    @staticmethod
    def get_event(ctx: RunContext[AgentDep], data: K8sData) -> str:
        """Get Kubernetes events - useful for troubleshooting"""
        logger.info("get event session_id:{}, event: {}", ctx.deps.session_id, data)
        return devtron_post_request_with_auth(data, 'k8s/events')

    @staticmethod
    def get_manifest(ctx: RunContext[AgentDep], data: K8sData) -> str:
        """Get resource manifest - for configuration analysis"""
        logger.info("get manifest session_id:{}, event: {}", ctx.deps.session_id, data)
        return devtron_post_request_with_auth(data, 'drift/managed-resource')

    @staticmethod
    def get_logs(ctx: RunContext[AgentDep], data: LogRequest) -> str:
        """Get pod logs - essential for debugging"""
        logger.info("get logs session_id:{}, event: {}", ctx.deps.session_id, data)
        params = data.to_dict()
        params['appId'] = "1|7222|340"
        params['appType'] = 0
        params['deploymentType'] = 0
        params.pop("podName", None)
        uri = "k8s/pods/logs/{}".format(data.podName)
        log = devtron_get_request_with_auth(params, uri)
        return log

    async def smart_retrieve_devtron_info(self, ctx: RunContext[AgentDep], search_query: str) -> str:
        """
        Smart documentation retrieval with query analysis and caching
        
        This tool now analyzes the query context to determine if documentation
        search is actually needed, reducing unnecessary calls.
        """
        # Get the original query context if available
        original_query = getattr(ctx.deps, 'query_context', search_query)
        
        # Classify the query to determine if documentation search is needed
        query_type = self.classifier.classify_query(original_query)
        
        # Check if we should skip documentation search for efficiency
        if self.router.should_skip_documentation_search(original_query, query_type):
            logger.info("Skipping documentation search - query can be answered with general knowledge")
            return "This query can be answered using general Kubernetes knowledge. No specific Devtron documentation needed."
        
        # Check cache first
        cache_key = search_query.lower().strip()
        if cache_key in self._doc_cache:
            logger.info("Returning cached documentation result for: {}", search_query)
            return self._doc_cache[cache_key]
        
        # Perform the search
        logger.info("Searching documentation for: {}", search_query)
        result = await search_vector_index(search_query)
        
        # Cache the result
        self._doc_cache[cache_key] = result
        
        return result

    @staticmethod
    async def retrieve_devtron_info(ctx: RunContext[AgentDep], search_query: str) -> str:
        """
        Legacy documentation retrieval - kept for backward compatibility
        Consider using smart_retrieve_devtron_info instead
        """
        logger.warning("Using legacy retrieve_devtron_info - consider upgrading to smart version")
        return await search_vector_index(search_query)


# Create an instance for the smart tools
smart_tools_instance = SmartDevtronTools()

# Enhanced tool functions list with smart routing
SMART_TOOL_FUNCTIONS = [
    SmartDevtronTools.get_app_detail,
    SmartDevtronTools.get_deployment_status,
    SmartDevtronTools.get_event,
    SmartDevtronTools.get_manifest,
    SmartDevtronTools.get_logs,
    smart_tools_instance.smart_retrieve_devtron_info,  # Use the smart version
]

# Legacy tool functions for backward compatibility
TOOL_FUNCTIONS = [
    SmartDevtronTools.get_app_detail,
    SmartDevtronTools.get_deployment_status,
    SmartDevtronTools.get_event,
    SmartDevtronTools.get_manifest,
    SmartDevtronTools.get_logs,
    SmartDevtronTools.retrieve_devtron_info,  # Legacy version
]
