#!/usr/bin/env python3
"""
Test script to compare original vs smart agent performance
"""
import asyncio
import time
import json
from loguru import logger
from devtron_agent_config import get_devtron_agent_config, get_smart_devtron_agent_config, create_smart_agent_deps
from query_classifier import QueryClassifier
from intelligent_tool_router import IntelligentToolRouter
from tool import AgentDep


class AgentPerformanceTester:
    def __init__(self):
        self.classifier = QueryClassifier()
        self.router = IntelligentToolRouter()
        self.test_results = []

    async def test_query(self, query: str, agent_type: str, agent, deps):
        """Test a single query and measure performance"""
        print(f"\n🔍 Testing {agent_type}: {query}")
        print("-" * 60)
        
        start_time = time.time()
        tool_calls = []
        
        try:
            # Run the agent
            async with agent.iter(query, deps=deps) as agent_run:
                async for node in agent_run:
                    if hasattr(node, 'model_response') and hasattr(node.model_response, 'parts'):
                        for part in node.model_response.parts:
                            if hasattr(part, 'tool_name'):
                                tool_calls.append(part.tool_name)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # Get the final result
            result = agent_run.result
            response_text = result.data if result else "No response"
            
            # Count token usage
            usage = agent_run.ctx.state.usage
            
            test_result = {
                'query': query,
                'agent_type': agent_type,
                'response_time': response_time,
                'tool_calls': tool_calls,
                'tool_count': len(tool_calls),
                'request_tokens': usage.request_tokens,
                'response_tokens': usage.response_tokens,
                'total_tokens': usage.total_tokens,
                'response_length': len(response_text),
                'success': True
            }
            
            print(f"✅ Success!")
            print(f"⏱️  Response Time: {response_time:.2f}s")
            print(f"🛠️  Tools Called: {tool_calls}")
            print(f"📊 Tokens: {usage.request_tokens} req + {usage.response_tokens} res = {usage.total_tokens} total")
            print(f"📝 Response Length: {len(response_text)} chars")
            
            return test_result
            
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            
            test_result = {
                'query': query,
                'agent_type': agent_type,
                'response_time': response_time,
                'tool_calls': [],
                'tool_count': 0,
                'request_tokens': 0,
                'response_tokens': 0,
                'total_tokens': 0,
                'response_length': 0,
                'success': False,
                'error': str(e)
            }
            
            print(f"❌ Error: {e}")
            return test_result

    async def run_comparison_tests(self):
        """Run comparison tests between original and smart agents"""
        
        # Test queries of different types
        test_queries = [
            # General knowledge - should be faster with smart agent
            "What is a Kubernetes pod?",
            "Explain the difference between deployment and statefulset",
            
            # Specific debugging - should use targeted tools
            "Why is application with application_id 1 and environment_id 1 unhealthy?",
            
            # Devtron features - should use documentation
            "How to install Devtron with Helm?",
            
            # Simple questions that shouldn't need tools
            "What is Kubernetes?",
        ]
        
        print("🚀 Starting Agent Performance Comparison")
        print("=" * 80)
        
        # Get both agents
        try:
            original_agent = await get_devtron_agent_config()
            smart_agent = await get_smart_devtron_agent_config()
            print("✅ Both agents initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize agents: {e}")
            return
        
        # Test each query with both agents
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Test {i}/{len(test_queries)}: {query}")
            print("=" * 80)
            
            # Analyze the query first
            query_type = self.classifier.classify_query(query)
            execution_plan = self.router.create_execution_plan(query)
            
            print(f"🎯 Query Classification: {query_type.value}")
            print(f"📋 Execution Strategy: {execution_plan.execution_strategy}")
            print(f"🛠️  Recommended Tools: {execution_plan.recommended_tools}")
            print(f"📚 Should Use Docs: {execution_plan.should_use_docs}")
            
            # Test original agent
            original_deps = AgentDep(session_id=f"test_original_{i}")
            original_result = await self.test_query(query, "Original", original_agent, original_deps)
            self.test_results.append(original_result)
            
            # Test smart agent
            smart_deps = create_smart_agent_deps(f"test_smart_{i}", query)
            smart_result = await self.test_query(query, "Smart", smart_agent, smart_deps)
            self.test_results.append(smart_result)
            
            # Compare results
            self.compare_results(original_result, smart_result)
            
            # Add a small delay between tests
            await asyncio.sleep(1)

    def compare_results(self, original_result, smart_result):
        """Compare results between original and smart agent"""
        print(f"\n📊 Comparison:")
        
        if original_result['success'] and smart_result['success']:
            # Response time comparison
            time_diff = original_result['response_time'] - smart_result['response_time']
            time_improvement = (time_diff / original_result['response_time']) * 100 if original_result['response_time'] > 0 else 0
            
            if time_diff > 0:
                print(f"⚡ Smart agent is {time_diff:.2f}s faster ({time_improvement:.1f}% improvement)")
            elif time_diff < 0:
                print(f"⚠️  Smart agent is {abs(time_diff):.2f}s slower ({abs(time_improvement):.1f}% slower)")
            else:
                print("⚖️  Similar response times")
            
            # Tool usage comparison
            original_tools = len(original_result['tool_calls'])
            smart_tools = len(smart_result['tool_calls'])
            
            if original_tools > smart_tools:
                print(f"🛠️  Smart agent used {original_tools - smart_tools} fewer tools ({original_tools} → {smart_tools})")
            elif original_tools < smart_tools:
                print(f"🛠️  Smart agent used {smart_tools - original_tools} more tools ({original_tools} → {smart_tools})")
            else:
                print(f"🛠️  Both agents used {original_tools} tools")
            
            # Token usage comparison
            token_diff = original_result['total_tokens'] - smart_result['total_tokens']
            if token_diff > 0:
                print(f"💰 Smart agent used {token_diff} fewer tokens")
            elif token_diff < 0:
                print(f"💰 Smart agent used {abs(token_diff)} more tokens")
            else:
                print("💰 Similar token usage")
        
        else:
            if not original_result['success']:
                print("❌ Original agent failed")
            if not smart_result['success']:
                print("❌ Smart agent failed")

    def generate_summary_report(self):
        """Generate a summary report of all tests"""
        print("\n" + "=" * 80)
        print("📈 PERFORMANCE SUMMARY REPORT")
        print("=" * 80)
        
        original_results = [r for r in self.test_results if r['agent_type'] == 'Original' and r['success']]
        smart_results = [r for r in self.test_results if r['agent_type'] == 'Smart' and r['success']]
        
        if not original_results or not smart_results:
            print("❌ Insufficient data for comparison")
            return
        
        # Average response times
        avg_original_time = sum(r['response_time'] for r in original_results) / len(original_results)
        avg_smart_time = sum(r['response_time'] for r in smart_results) / len(smart_results)
        
        # Average tool usage
        avg_original_tools = sum(r['tool_count'] for r in original_results) / len(original_results)
        avg_smart_tools = sum(r['tool_count'] for r in smart_results) / len(smart_results)
        
        # Average token usage
        avg_original_tokens = sum(r['total_tokens'] for r in original_results) / len(original_results)
        avg_smart_tokens = sum(r['total_tokens'] for r in smart_results) / len(smart_results)
        
        print(f"⏱️  Average Response Time:")
        print(f"   Original: {avg_original_time:.2f}s")
        print(f"   Smart:    {avg_smart_time:.2f}s")
        print(f"   Improvement: {((avg_original_time - avg_smart_time) / avg_original_time * 100):.1f}%")
        
        print(f"\n🛠️  Average Tool Usage:")
        print(f"   Original: {avg_original_tools:.1f} tools")
        print(f"   Smart:    {avg_smart_tools:.1f} tools")
        print(f"   Reduction: {((avg_original_tools - avg_smart_tools) / avg_original_tools * 100):.1f}%")
        
        print(f"\n💰 Average Token Usage:")
        print(f"   Original: {avg_original_tokens:.0f} tokens")
        print(f"   Smart:    {avg_smart_tokens:.0f} tokens")
        print(f"   Reduction: {((avg_original_tokens - avg_smart_tokens) / avg_original_tokens * 100):.1f}%")
        
        # Save detailed results to file
        with open('agent_performance_results.json', 'w') as f:
            json.dump(self.test_results, f, indent=2)
        print(f"\n💾 Detailed results saved to: agent_performance_results.json")


async def main():
    """Main test function"""
    tester = AgentPerformanceTester()
    
    try:
        await tester.run_comparison_tests()
        tester.generate_summary_report()
        
        print("\n🎉 Performance testing completed!")
        print("\nKey Findings:")
        print("✅ Smart agent should show reduced tool usage for general knowledge queries")
        print("✅ Smart agent should maintain accuracy while improving efficiency")
        print("✅ Smart agent should provide faster responses for simple questions")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
