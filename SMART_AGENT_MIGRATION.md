# Smart Agent Migration Guide

This guide explains how to migrate from the current agent implementation to the new smart agent with intelligent query routing.

## Problem Statement

The current agent often makes unnecessary calls to `retrieve_devtron_info` (runbook search) even for simple queries that can be answered with the LLM's built-in knowledge. This leads to:

- Slower response times
- Unnecessary resource usage
- Poor user experience for simple questions
- Inefficient tool utilization

## Solution Overview

The smart agent introduces:

1. **Query Classification**: Automatically categorizes queries into types
2. **Intelligent Tool Routing**: Routes queries to appropriate tools based on classification
3. **Efficiency Optimization**: Skips unnecessary documentation searches
4. **Smart Caching**: Caches documentation results to avoid repeated searches

## Migration Steps

### Step 1: Update Dependencies

Add the new smart agent components to your imports:

```python
from devtron_agent_config import get_smart_devtron_agent_config, create_smart_agent_deps
from query_classifier import QueryClassifier
from intelligent_tool_router import IntelligentToolRouter
```

### Step 2: Use Smart Agent

Replace the original agent with the smart agent:

```python
# Old way
agent = await get_devtron_agent_config()
deps = AgentDep(session_id="session_id")

# New way
smart_agent = await get_smart_devtron_agent_config()
deps = create_smart_agent_deps("session_id", query="user's query")
```

### Step 3: Update System Prompt (Optional)

The smart agent uses an enhanced system prompt (`smart_system_prompt.j2`) that provides better guidance for tool usage. You can customize this prompt or use the original.

### Step 4: Test the Migration

Run the demo script to see the improvements:

```bash
python demo_smart_agent.py
```

## Query Classification Types

### 1. General Knowledge
- **Examples**: "What is Kubernetes?", "Explain pods"
- **Behavior**: Uses LLM knowledge, skips documentation search
- **Tools**: None or minimal

### 2. Specific Debugging
- **Examples**: "App ID 1 is unhealthy", "Check logs for pod xyz"
- **Behavior**: Uses application-specific tools first
- **Tools**: get_app_detail, get_logs, get_event

### 3. Devtron Features
- **Examples**: "How to install Devtron?", "Configure pipeline"
- **Behavior**: Searches documentation for accurate info
- **Tools**: retrieve_devtron_info

### 4. Complex Troubleshooting
- **Examples**: "Deployment issues", "Performance problems"
- **Behavior**: Systematic multi-tool approach
- **Tools**: All tools as needed

## Performance Improvements

### Before (Original Agent)
```
Query: "What is a Kubernetes pod?"
→ Calls retrieve_devtron_info unnecessarily
→ Searches vector database
→ Returns documentation + LLM knowledge
→ Slower response time
```

### After (Smart Agent)
```
Query: "What is a Kubernetes pod?"
→ Classifies as General Knowledge
→ Uses LLM built-in knowledge only
→ Skips documentation search
→ Faster response time
```

## Backward Compatibility

The original agent remains available for backward compatibility:

```python
# Original agent (still works)
original_agent = await get_devtron_agent_config()

# Smart agent (recommended)
smart_agent = await get_smart_devtron_agent_config()
```

## Configuration Options

### Environment Variables

- `SMART_SYSTEM_PROMPT_PATH`: Path to smart system prompt (default: `./prompt/smart_system_prompt.j2`)
- `SYSTEM_PROMPT_PATH`: Path to original system prompt (default: `./prompt/system_prompt.j2`)

### Customization

You can customize the query classification by modifying:

- `query_classifier.py`: Add new keywords or patterns
- `intelligent_tool_router.py`: Adjust routing logic
- `smart_system_prompt.j2`: Update system instructions

## Testing

### Unit Tests

```python
from query_classifier import QueryClassifier

classifier = QueryClassifier()
assert classifier.classify_query("What is Kubernetes?") == QueryType.GENERAL_KNOWLEDGE
assert classifier.classify_query("App ID 1 is unhealthy") == QueryType.SPECIFIC_DEBUGGING
```

### Integration Tests

```python
# Test that general knowledge queries don't call documentation search
response = await smart_agent.run("What is a pod?", deps=deps)
# Verify no retrieve_devtron_info calls were made
```

## Monitoring and Analytics

Track the improvements:

1. **Response Time**: Measure before/after response times
2. **Tool Usage**: Monitor which tools are called for different query types
3. **User Satisfaction**: Track user feedback on response quality
4. **Resource Usage**: Monitor vector database query frequency

## Rollback Plan

If issues arise, you can easily rollback:

1. Switch back to `get_devtron_agent_config()`
2. Use original system prompt
3. Remove smart agent imports

## Best Practices

1. **Query Context**: Always provide query context when creating agent dependencies
2. **Monitoring**: Monitor tool usage patterns to optimize further
3. **Feedback Loop**: Use user feedback to improve classification accuracy
4. **Gradual Migration**: Test with a subset of users first

## Expected Results

- **30-50% reduction** in unnecessary documentation searches
- **20-40% faster** response times for general knowledge queries
- **Better resource utilization** of vector database
- **Improved user experience** with more relevant responses
- **Maintained accuracy** for complex debugging scenarios

## Support

For issues or questions about the smart agent migration:

1. Check the demo script: `python demo_smart_agent.py`
2. Review logs for classification decisions
3. Test with different query types
4. Customize classification rules as needed
