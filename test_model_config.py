#!/usr/bin/env python3
"""
Test script to verify the model configuration is working correctly.
"""

import asyncio
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Load environment variables
load_dotenv()

async def test_model_config():
    """Test the model configuration system"""
    print("🔧 Testing Model Configuration...")
    
    try:
        from model_config import get_model_config
        
        config = get_model_config()
        print(f"✓ Model Provider: {config.provider.value}")
        print(f"✓ Model Name: {config.model_name}")
        print(f"✓ Model String: {config.model_string}")
        print(f"✓ API Key Configured: {bool(config.api_key)}")
        print(f"✓ Configuration Valid: {config.validate()}")
        
        return config.validate()
    except Exception as e:
        print(f"✗ Model configuration failed: {e}")
        return False

async def test_agent_initialization():
    """Test agent initialization with the new model"""
    print("\n🤖 Testing Agent Initialization...")
    
    try:
        from devtron_agent_config import get_devtron_agent_config
        
        agent = await get_devtron_agent_config()
        print("✓ Agent initialized successfully")
        print(f"✓ Agent model: {agent.model}")
        
        return True
    except Exception as e:
        print(f"✗ Agent initialization failed: {e}")
        return False

async def test_simple_query():
    """Test a simple query to the agent"""
    print("\n💬 Testing Simple Query...")
    
    try:
        from devtron_agent_config import get_devtron_agent_config
        
        agent = await get_devtron_agent_config()
        
        # Simple test query
        test_message = "Hello! Can you tell me what Devtron is?"
        
        print(f"Sending query: {test_message}")
        
        # Create a simple dependency object
        from dataclasses import dataclass
        
        @dataclass
        class AgentDep:
            session_id: str = "test-session"
        
        deps = AgentDep()
        
        # Run the agent
        response = await agent.run(test_message, deps=deps)
        
        print(f"✓ Response received: {response.data[:200]}...")
        return True
        
    except Exception as e:
        print(f"✗ Simple query failed: {e}")
        return False

async def test_database_connection():
    """Test database connection"""
    print("\n🗄️  Testing Database Connection...")
    
    try:
        from db.repository import Database
        
        async with Database.connect() as db:
            result = await db.pool.fetchval("SELECT 1")
            print(f"✓ Database connection successful: {result}")
            return True
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Testing Devtron Agent with OpenAI\n")
    
    tests = [
        ("Model Configuration", test_model_config),
        ("Database Connection", test_database_connection),
        ("Agent Initialization", test_agent_initialization),
        ("Simple Query", test_simple_query),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! The OpenAI agent is working correctly.")
        print("\nYou can now start the servers:")
        print("1. MCP Server: python devtron_mcp/server.py")
        print("2. Main Server: python server.py")
    else:
        print(f"\n❌ {total - passed} tests failed. Please check the configuration.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
