import asyncio
import time
from loguru import logger
from devtron_agent_config import get_devtron_agent_config, get_smart_devtron_agent_config, create_smart_agent_deps
from query_classifier import QueryClassifier
from intelligent_tool_router import IntelligentToolRouter


async def compare_agents():
    """Compare the behavior of original vs smart agent"""
    
    # Test queries of different types
    test_queries = [
        # General knowledge - should NOT use documentation search
        "What is a Kubernetes pod?",
        "Explain the difference between deployment and statefulset",
        "What is a namespace in Kubernetes?",
        
        # Specific debugging - should use app-specific tools first
        "Why is application with application_id 1 and environment_id 1 unhealthy?",
        "Check logs for pod xyz-123 in namespace production",
        
        # Devtron features - should use documentation search
        "How to install Devtron with Helm?",
        "Configure CI/CD pipeline in Devtron",
        "Devtron security scanning features",
        
        # Complex troubleshooting - should use systematic approach
        "Application deployment is failing intermittently"
    ]
    
    # Get both agents
    original_agent = await get_devtron_agent_config()
    smart_agent = await get_smart_devtron_agent_config()
    
    # Initialize classifier and router for analysis
    classifier = QueryClassifier()
    router = IntelligentToolRouter()
    
    print("🔍 Agent Comparison Demo")
    print("=" * 50)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Test Query {i}: {query}")
        print("-" * 40)
        
        # Analyze the query
        query_type = classifier.classify_query(query)
        execution_plan = router.create_execution_plan(query)
        
        print(f"🎯 Query Type: {query_type.value}")
        print(f"📋 Execution Plan: {execution_plan.execution_strategy}")
        print(f"🛠️  Recommended Tools: {execution_plan.recommended_tools}")
        print(f"📚 Should Use Docs: {execution_plan.should_use_docs}")
        print(f"🎯 Confidence: {execution_plan.confidence:.2f}")
        
        # Show what the smart agent would do differently
        if router.should_skip_documentation_search(query, query_type):
            print("✅ Smart Agent: Will skip unnecessary documentation search")
        else:
            print("📖 Smart Agent: Will search documentation as needed")
        
        print()


async def demo_smart_routing():
    """Demonstrate smart routing in action"""
    
    print("\n🚀 Smart Routing Demo")
    print("=" * 50)
    
    # Get the smart agent
    smart_agent = await get_smart_devtron_agent_config()
    
    # Test with a general knowledge query
    general_query = "What is a Kubernetes pod?"
    print(f"\n📝 Testing General Knowledge Query: {general_query}")
    
    deps = create_smart_agent_deps("demo_session", general_query)
    
    try:
        start_time = time.time()
        response = await smart_agent.run(general_query, deps=deps)
        end_time = time.time()
        
        print(f"⏱️  Response Time: {end_time - start_time:.2f} seconds")
        print(f"📄 Response: {response.data[:200]}...")
        
        # Check if any tools were called
        if hasattr(response, 'all_messages'):
            messages = response.all_messages()
            tool_calls = [msg for msg in messages if hasattr(msg, 'parts') and 
                         any(hasattr(part, 'tool_name') for part in getattr(msg, 'parts', []))]
            
            if tool_calls:
                print(f"🛠️  Tools Called: {len(tool_calls)}")
                for call in tool_calls:
                    for part in call.parts:
                        if hasattr(part, 'tool_name'):
                            print(f"   - {part.tool_name}")
            else:
                print("✅ No unnecessary tool calls made!")
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def demo_efficiency_comparison():
    """Show efficiency improvements"""
    
    print("\n⚡ Efficiency Comparison")
    print("=" * 50)
    
    # Queries that should be handled differently
    efficient_queries = [
        ("What is Kubernetes?", "Should use built-in knowledge"),
        ("How to install Devtron?", "Should search documentation"),
        ("App ID 1 is unhealthy", "Should check app status first"),
    ]
    
    classifier = QueryClassifier()
    router = IntelligentToolRouter()
    
    for query, expected_behavior in efficient_queries:
        print(f"\n📝 Query: {query}")
        print(f"🎯 Expected: {expected_behavior}")
        
        query_type = classifier.classify_query(query)
        plan = router.create_execution_plan(query)
        
        print(f"📊 Classification: {query_type.value}")
        print(f"🛠️  Tools: {plan.recommended_tools}")
        
        if plan.execution_strategy == "direct_response":
            print("✅ Will use built-in knowledge (efficient!)")
        elif plan.execution_strategy == "documentation_first":
            print("📚 Will search documentation first")
        elif plan.execution_strategy == "sequential_debugging":
            print("🔧 Will use systematic debugging approach")
        else:
            print("🔍 Will use comprehensive analysis")


async def main():
    """Run all demos"""
    logger.info("Starting Smart Agent Demo")
    
    try:
        await compare_agents()
        await demo_smart_routing()
        await demo_efficiency_comparison()
        
        print("\n🎉 Demo completed successfully!")
        print("\nKey Improvements:")
        print("✅ Intelligent query classification")
        print("✅ Reduced unnecessary tool calls")
        print("✅ Optimized response times")
        print("✅ Better resource utilization")
        print("✅ Maintained accuracy and functionality")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"❌ Demo failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
