#!/usr/bin/env python3
"""
Simple test to verify OpenAI agent is working correctly.
"""

import asyncio
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Load environment variables
load_dotenv()

async def test_openai_agent():
    """Test the OpenAI agent with a simple query"""
    print("🤖 Testing OpenAI Agent...")
    
    try:
        # Import the agent configuration
        from devtron_agent_config import get_devtron_agent_config
        
        # Get the configured agent
        agent = await get_devtron_agent_config()
        print(f"✓ Agent initialized with model: {agent.model}")
        
        # Create a simple dependency object
        from dataclasses import dataclass
        
        @dataclass
        class AgentDep:
            session_id: str = "test-session"
        
        deps = AgentDep()
        
        # Test with a simple query that doesn't require external tools
        test_message = "What is Devtron? Give me a brief explanation."
        
        print(f"📝 Sending query: {test_message}")
        
        # Run the agent
        response = await agent.run(test_message, deps=deps)
        
        print(f"✅ Response received:")
        print(f"📄 {response.data}")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenAI agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_model_info():
    """Test model configuration info"""
    print("\n🔧 Model Configuration Info:")
    
    try:
        from model_config import get_model_config
        
        config = get_model_config()
        info = config.get_model_info()
        
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        return True
    except Exception as e:
        print(f"❌ Model info test failed: {e}")
        return False

async def main():
    """Run the test"""
    print("🚀 Testing Devtron Agent with OpenAI\n")
    
    # Test model configuration
    await test_model_info()
    
    # Test the agent
    success = await test_openai_agent()
    
    if success:
        print("\n🎉 OpenAI agent is working correctly!")
        print("\nYou can now:")
        print("1. Start the main server: python server.py")
        print("2. Test the chat API: curl -X POST http://localhost:8000/chat \\")
        print("   -H 'Content-Type: application/json' \\")
        print("   -d '{\"message\": \"Hello!\", \"session_id\": \"test\"}'")
    else:
        print("\n❌ OpenAI agent test failed. Please check the configuration.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
