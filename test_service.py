#!/usr/bin/env python3
"""
Simple test script to verify the Devtron Agent service is working.
This script tests the basic functionality without requiring a real Google API key.
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

async def test_database_connection():
    """Test database connection"""
    try:
        from db.repository import Database
        print("✓ Testing database connection...")

        async with Database.connect() as db:
            # Test basic database operations using the pool
            result = await db.pool.fetchval("SELECT 1 as test")
            print(f"✓ Database connection successful: {result}")
            return True
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False

async def test_vector_store():
    """Test vector store initialization"""
    try:
        print("✓ Testing vector store...")
        from vector_store import VectorStoreManager

        # This will initialize the vector store
        vector_store = VectorStoreManager.get_vector_store_with_embeddings()
        print("✓ Vector store initialized successfully")
        return True
    except Exception as e:
        print(f"✗ Vector store initialization failed: {e}")
        return False

def test_mcp_server():
    """Test if MCP server is running"""
    try:
        import requests
        print("✓ Testing MCP server connection...")

        # Test the SSE endpoint which is the actual MCP server endpoint
        response = requests.get("http://localhost:9000/sse/", timeout=5, stream=True)
        if response.status_code == 200:
            print("✓ MCP server is running")
            return True
        else:
            print(f"✗ MCP server returned status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ MCP server connection failed: {e}")
        return False

def test_environment_variables():
    """Test if required environment variables are set"""
    print("✓ Testing environment variables...")
    
    required_vars = [
        'DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER',
        'DEVTRON_MCP_SERVER_URL', 'SYSTEM_PROMPT_PATH'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"✗ Missing environment variables: {missing_vars}")
        return False
    else:
        print("✓ All required environment variables are set")
        return True

async def main():
    """Run all tests"""
    print("🚀 Testing Devtron Agent Service\n")
    
    # Load environment variables from .env file
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✓ Loaded environment variables from .env file")
    except ImportError:
        print("⚠️  python-dotenv not installed, using system environment variables")
    except Exception as e:
        print(f"⚠️  Could not load .env file: {e}")
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("MCP Server", test_mcp_server),
        ("Database Connection", test_database_connection),
        ("Vector Store", test_vector_store),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! The service is ready to run.")
        print("\nNext steps:")
        print("1. Set a valid GOOGLE_API_KEY in .env file")
        print("2. Set DEVTRON_URL and DEVTRON_TOKEN for Devtron integration")
        print("3. Start the servers:")
        print("   - MCP Server: python devtron_mcp/server.py")
        print("   - Main Server: python server.py")
    else:
        print(f"\n❌ {total - passed} tests failed. Please fix the issues before running the service.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
