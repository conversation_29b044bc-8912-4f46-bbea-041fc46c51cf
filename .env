# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=app_db
DB_USER=postgres
DB_PASSWORD=

# Vector Database Configuration (using same DB for simplicity)
VECTOR_DB_NAME=app_db

# Server Configuration
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=debug
WORKERS=1
UVICORN_RELOAD=FALSE

# Devtron Configuration (these need to be set for actual usage)
DEVTRON_URL=https://your-devtron-instance.com
DEVTRON_TOKEN=your-devtron-token

# MCP Server Configuration
DEVTRON_MCP_SERVER_URL=http://127.0.0.1:9000/sse/

# System Prompt Configuration
SYSTEM_PROMPT_PATH=./prompt/system_prompt.j2

# Repository Configuration for indexing
REPO_URL=https://github.com/devtron-labs/devtron-documentation.git
DOCUMENT_LOCATION=/tmp/devtron-documentation

# AI Model Configuration
AI_MODEL_PROVIDER=openai
AI_MODEL=gpt-4o-mini
OPENAI_API_KEY="********************************************************************************************************************************************************************"

# Google API Key (optional, for Google models)
# Get your API key from: https://aistudio.google.com/app/apikey
GOOGLE_API_KEY=test-key-placeholder