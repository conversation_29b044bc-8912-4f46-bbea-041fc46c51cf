"""
Model configuration system for Devtron Agent.
Supports OpenAI, Google (Gemini), and Anthropic (<PERSON>) models.
"""

import os
from enum import Enum
from typing import Optional, Dict, Any
from loguru import logger


class ModelProvider(str, Enum):
    """Supported AI model providers"""
    OPENAI = "openai"
    GOOGLE = "google"
    ANTHROPIC = "anthropic"


class ModelConfig:
    """Configuration class for AI models"""
    
    # Default models for each provider
    DEFAULT_MODELS = {
        ModelProvider.OPENAI: "gpt-4o",
        ModelProvider.GOOGLE: "gemini-2.0-flash",
        ModelProvider.ANTHROPIC: "claude-3-5-sonnet-20241022"
    }
    
    # Model name mappings for pydantic-ai
    MODEL_MAPPINGS = {
        # OpenAI models
        "gpt-4o": "openai:gpt-4o",
        "gpt-4o-mini": "openai:gpt-4o-mini", 
        "gpt-4-turbo": "openai:gpt-4-turbo",
        "gpt-3.5-turbo": "openai:gpt-3.5-turbo",
        
        # Google models
        "gemini-2.0-flash": "google-gla:gemini-2.0-flash",
        "gemini-1.5-pro": "google-gla:gemini-1.5-pro",
        "gemini-1.5-flash": "google-gla:gemini-1.5-flash",
        
        # Anthropic models
        "claude-3-5-sonnet-20241022": "anthropic:claude-3-5-sonnet-20241022",
        "claude-3-5-haiku-20241022": "anthropic:claude-3-5-haiku-20241022",
        "claude-3-opus-20240229": "anthropic:claude-3-opus-20240229",
    }
    
    def __init__(self):
        self.provider = self._get_provider()
        self.model_name = self._get_model_name()
        self.api_key = self._get_api_key()
        self.model_string = self._get_model_string()
        
    def _get_provider(self) -> ModelProvider:
        """Get the model provider from environment variables"""
        provider_str = os.getenv("AI_MODEL_PROVIDER", "google").lower()
        
        try:
            return ModelProvider(provider_str)
        except ValueError:
            logger.warning(f"Invalid model provider '{provider_str}', defaulting to Google")
            return ModelProvider.GOOGLE
    
    def _get_model_name(self) -> str:
        """Get the specific model name from environment variables"""
        # First check for provider-specific model env var
        provider_model_var = f"{self.provider.value.upper()}_MODEL"
        model_name = os.getenv(provider_model_var)
        
        if model_name:
            return model_name
            
        # Fall back to generic AI_MODEL env var
        model_name = os.getenv("AI_MODEL")
        
        if model_name:
            return model_name
            
        # Use default model for the provider
        return self.DEFAULT_MODELS[self.provider]
    
    def _get_api_key(self) -> Optional[str]:
        """Get the API key for the selected provider"""
        api_key_vars = {
            ModelProvider.OPENAI: ["OPENAI_API_KEY"],
            ModelProvider.GOOGLE: ["GOOGLE_API_KEY", "GEMINI_API_KEY"],
            ModelProvider.ANTHROPIC: ["ANTHROPIC_API_KEY", "CLAUDE_API_KEY"]
        }
        
        for var_name in api_key_vars[self.provider]:
            api_key = os.getenv(var_name)
            if api_key and api_key != "test-key-placeholder":
                return api_key
        
        logger.warning(f"No valid API key found for {self.provider.value}")
        return None
    
    def _get_model_string(self) -> str:
        """Get the pydantic-ai compatible model string"""
        if self.model_name in self.MODEL_MAPPINGS:
            return self.MODEL_MAPPINGS[self.model_name]
        
        # If not in mappings, construct the string
        return f"{self.provider.value}:{self.model_name}"
    
    def validate(self) -> bool:
        """Validate the model configuration"""
        if not self.api_key:
            logger.error(f"No API key configured for {self.provider.value}")
            return False
            
        if self.model_name not in self.MODEL_MAPPINGS and not self.model_name:
            logger.warning(f"Model '{self.model_name}' not in known models list")
            
        return True
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model configuration information"""
        return {
            "provider": self.provider.value,
            "model_name": self.model_name,
            "model_string": self.model_string,
            "api_key_configured": bool(self.api_key),
            "is_valid": self.validate()
        }
    
    def get_environment_variables(self) -> Dict[str, str]:
        """Get the environment variables that should be set for this configuration"""
        env_vars = {
            "AI_MODEL_PROVIDER": self.provider.value,
            "AI_MODEL": self.model_name,
        }
        
        # Add provider-specific API key variable
        if self.provider == ModelProvider.OPENAI:
            env_vars["OPENAI_API_KEY"] = "your-openai-api-key"
        elif self.provider == ModelProvider.GOOGLE:
            env_vars["GOOGLE_API_KEY"] = "your-google-api-key"
        elif self.provider == ModelProvider.ANTHROPIC:
            env_vars["ANTHROPIC_API_KEY"] = "your-anthropic-api-key"
            
        return env_vars


def get_model_config() -> ModelConfig:
    """Get the current model configuration"""
    return ModelConfig()


def list_available_models() -> Dict[str, list]:
    """List all available models by provider"""
    models_by_provider = {}
    
    for model_name, model_string in ModelConfig.MODEL_MAPPINGS.items():
        provider = model_string.split(":")[0]
        if provider not in models_by_provider:
            models_by_provider[provider] = []
        models_by_provider[provider].append(model_name)
    
    return models_by_provider


def validate_model_configuration() -> bool:
    """Validate the current model configuration"""
    config = get_model_config()
    return config.validate()


if __name__ == "__main__":
    # Test the configuration
    config = get_model_config()
    print("Model Configuration:")
    print(f"Provider: {config.provider}")
    print(f"Model: {config.model_name}")
    print(f"Model String: {config.model_string}")
    print(f"API Key Configured: {bool(config.api_key)}")
    print(f"Valid: {config.validate()}")
    
    print("\nAvailable Models:")
    for provider, models in list_available_models().items():
        print(f"{provider}: {', '.join(models)}")
