#!/usr/bin/env python3
"""
Simple test script to verify agent setup and basic functionality
"""
import asyncio
import os
from loguru import logger
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_model_config():
    """Test model configuration"""
    print("🔧 Testing Model Configuration...")
    
    try:
        from model_config import get_model_config
        
        config = get_model_config()
        info = config.get_model_info()
        
        print(f"✅ Model Provider: {info['provider']}")
        print(f"✅ Model Name: {info['model_name']}")
        print(f"✅ Model String: {info['model_string']}")
        print(f"✅ API Key Configured: {info['api_key_configured']}")
        print(f"✅ Valid Configuration: {info['is_valid']}")
        
        return info['is_valid']
        
    except Exception as e:
        print(f"❌ Model config test failed: {e}")
        return False

async def test_original_agent():
    """Test the original agent"""
    print("\n🤖 Testing Original Agent...")
    
    try:
        from devtron_agent_config import get_devtron_agent_config
        from tool import AgentDep
        
        agent = await get_devtron_agent_config()
        print("✅ Original agent initialized")
        
        # Test with a simple query
        deps = AgentDep(session_id="test_session")
        query = "What is Kubernetes?"
        
        print(f"📝 Testing query: {query}")
        response = await agent.run(query, deps=deps)
        
        print(f"✅ Response received: {response.data[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Original agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_smart_agent():
    """Test the smart agent"""
    print("\n🧠 Testing Smart Agent...")
    
    try:
        from devtron_agent_config import get_smart_devtron_agent_config, create_smart_agent_deps
        
        agent = await get_smart_devtron_agent_config()
        print("✅ Smart agent initialized")
        
        # Test with a simple query
        query = "What is Kubernetes?"
        deps = create_smart_agent_deps("test_session", query)
        
        print(f"📝 Testing query: {query}")
        response = await agent.run(query, deps=deps)
        
        print(f"✅ Response received: {response.data[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Smart agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_query_classification():
    """Test query classification"""
    print("\n🎯 Testing Query Classification...")
    
    try:
        from query_classifier import QueryClassifier, QueryType
        
        classifier = QueryClassifier()
        
        test_queries = [
            ("What is Kubernetes?", QueryType.GENERAL_KNOWLEDGE),
            ("Application ID 1 is unhealthy", QueryType.SPECIFIC_DEBUGGING),
            ("How to install Devtron?", QueryType.DEVTRON_FEATURES),
        ]
        
        for query, expected_type in test_queries:
            classified_type = classifier.classify_query(query)
            status = "✅" if classified_type == expected_type else "⚠️"
            print(f"{status} '{query}' → {classified_type.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Query classification test failed: {e}")
        return False

async def test_mcp_connection():
    """Test MCP server connection"""
    print("\n🔗 Testing MCP Server Connection...")
    
    try:
        import httpx
        
        mcp_url = os.getenv("DEVTRON_MCP_SERVER_URL", "http://127.0.0.1:9000/sse/")
        
        # Try to connect to the MCP server
        async with httpx.AsyncClient() as client:
            response = await client.get(mcp_url.replace("/sse/", "/"))
            
        if response.status_code == 200:
            print(f"✅ MCP server is running at {mcp_url}")
            return True
        else:
            print(f"⚠️ MCP server responded with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ MCP connection test failed: {e}")
        print("💡 Make sure the MCP server is running: python devtron_mcp/server.py")
        return False

async def run_simple_comparison():
    """Run a simple comparison between agents"""
    print("\n⚖️ Simple Agent Comparison...")
    
    try:
        from devtron_agent_config import get_devtron_agent_config, get_smart_devtron_agent_config, create_smart_agent_deps
        from tool import AgentDep
        import time
        
        # Get both agents
        original_agent = await get_devtron_agent_config()
        smart_agent = await get_smart_devtron_agent_config()
        
        # Test query that should be faster with smart agent
        query = "What is a Kubernetes pod?"
        
        # Test original agent
        print(f"🔄 Testing original agent with: {query}")
        start_time = time.time()
        original_deps = AgentDep(session_id="test_original")
        original_response = await original_agent.run(query, deps=original_deps)
        original_time = time.time() - start_time
        
        # Test smart agent
        print(f"🔄 Testing smart agent with: {query}")
        start_time = time.time()
        smart_deps = create_smart_agent_deps("test_smart", query)
        smart_response = await smart_agent.run(query, deps=smart_deps)
        smart_time = time.time() - start_time
        
        # Compare results
        print(f"\n📊 Results:")
        print(f"⏱️ Original Agent: {original_time:.2f}s")
        print(f"⏱️ Smart Agent: {smart_time:.2f}s")
        
        if smart_time < original_time:
            improvement = ((original_time - smart_time) / original_time) * 100
            print(f"🚀 Smart agent is {improvement:.1f}% faster!")
        else:
            print("📈 Performance similar or original was faster")
        
        print(f"\n📝 Original Response: {original_response.data[:150]}...")
        print(f"📝 Smart Response: {smart_response.data[:150]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Comparison test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests"""
    print("🚀 Devtron Agent Simple Test Suite")
    print("=" * 50)
    
    tests = [
        ("Model Configuration", test_model_config),
        ("MCP Connection", test_mcp_connection),
        ("Query Classification", test_query_classification),
        ("Original Agent", test_original_agent),
        ("Smart Agent", test_smart_agent),
        ("Simple Comparison", run_simple_comparison),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("📋 TEST SUMMARY")
    print(f"{'='*50}")
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"\n🎯 Overall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! The smart agent is ready for use.")
    else:
        print("⚠️ Some tests failed. Please check the configuration.")

if __name__ == "__main__":
    asyncio.run(main())
