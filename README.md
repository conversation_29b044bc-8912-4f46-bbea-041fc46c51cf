# Devtron Agent

This is a Devtron Agent - an AI-powered assistant that helps you debug Kubernetes applications and provides information about the Devtron platform. The agent uses Google's Gemini 2.0 Flash model with custom tools and integrates with Devtron APIs through a Model Context Protocol (MCP) server.

## Features

- **AI-Powered Debugging**: Uses Google Gemini 2.0 Flash for intelligent responses
- **Devtron API Integration**: Direct integration with Devtron APIs through MCP server
- **Vector Search**: RAG (Retrieval Augmented Generation) with Devtron documentation
- **Chat History**: Persistent chat history stored in PostgreSQL
- **FastAPI Backend**: RESTful API with automatic documentation

## Architecture

The system consists of two main components:

1. **Main Server** (`server.py`) - FastAPI server providing chat API endpoint (port 8000)
2. **MCP Server** (`devtron_mcp/server.py`) - Model Context Protocol server for Devtron API integration (port 9000)

## Prerequisites

- Python 3.10 or higher
- PostgreSQL database
- Google API key for Gemini (for AI functionality)
- Devtron instance URL and token (for Devtron integration)

## Installation

### 1. <PERSON><PERSON> the Repository

```bash
git clone <repository-url>
cd devtron-agent
```

### 2. Set up Python Environment

Using Poetry (recommended):
```bash
# Install Poetry if you haven't already
curl -sSL https://install.python-poetry.org | python3 -

# Install dependencies
poetry install
```

Or using pip with virtual environment:
```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install fastapi uvicorn loguru pydantic-ai fastmcp deepmerge psycopg2-binary \
           langchain-community langchain-postgres fastembed "unstructured[md]" \
           "pydantic-ai-slim[duckduckgo]" python-multipart jinja2
```

### 3. Set up PostgreSQL Database

Install and start PostgreSQL:
```bash
# On macOS with Homebrew
brew install postgresql@14
brew services start postgresql@14

# Create database
createdb app_db
```

### 4. Configure Environment Variables

Create a `.env` file in the project root:

```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=app_db
DB_USER=postgres
DB_PASSWORD=

# Vector Database Configuration (using same DB for simplicity)
VECTOR_DB_NAME=app_db

# Server Configuration
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=debug
WORKERS=1
UVICORN_RELOAD=FALSE

# Devtron Configuration (required for actual usage)
DEVTRON_URL=https://your-devtron-instance.com
DEVTRON_TOKEN=your-devtron-token

# MCP Server Configuration
DEVTRON_MCP_SERVER_URL=http://127.0.0.1:9000/mcp1/

# System Prompt Configuration
SYSTEM_PROMPT_PATH=./prompt/system_prompt.j2

# Repository Configuration for indexing
REPO_URL=https://github.com/devtron-labs/devtron-documentation.git
DOCUMENT_LOCATION=/tmp/devtron-documentation

# Google API Key (required for AI functionality)
GOOGLE_API_KEY=your-google-api-key
```

**Important**: You need to set up:
- `GOOGLE_API_KEY`: Get from [Google AI Studio](https://aistudio.google.com/app/apikey)
- `DEVTRON_URL` and `DEVTRON_TOKEN`: Your Devtron instance details

## Running the Service

### Step 1: Start the MCP Server

The MCP server provides Devtron API integration:

```bash
# Using Poetry
poetry run python devtron_mcp/server.py

# Or with virtual environment
source venv/bin/activate
python devtron_mcp/server.py
```

The MCP server will start on `http://0.0.0.0:9000`

### Step 2: Start the Main Server

In a new terminal, start the main FastAPI server:

```bash
# Using Poetry
poetry run python server.py

# Or with virtual environment
source venv/bin/activate
python server.py
```

The main server will start on `http://0.0.0.0:8000`

**Note**: On first startup, the server will download the embedding model (~1.3GB), which may take several minutes.

## Usage

### API Endpoints

Once both servers are running, you can access:

- **Main API**: `http://localhost:8000`
- **API Documentation**: `http://localhost:8000/docs`
- **Health Check**: `http://localhost:8000/health`
- **MCP Server**: `http://localhost:9000`

### Chat API

Send a POST request to `/chat` endpoint:

```bash
curl -X POST "http://localhost:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{
       "message": "How do I debug a failed CI pipeline in Devtron?",
       "session_id": "test-session"
     }'
```

### Example Chat Request

```json
{
  "message": "What are the common causes of deployment failures in Devtron?",
  "session_id": "user-123"
}
```

### Example Response

```json
{
  "response": "Based on the Devtron documentation, common causes of deployment failures include...",
  "session_id": "user-123"
}
```

## Testing the Service

### 1. Health Check

```bash
curl -X GET http://localhost:8000/health
```

Expected response:
```json
{"status": "healthy"}
```

### 2. Test Chat Endpoint

```bash
curl -X POST "http://localhost:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{
       "message": "Hello, can you help me with Devtron?",
       "session_id": "test-session"
     }'
```

### 3. Check API Documentation

Visit `http://localhost:8000/docs` in your browser to see the interactive API documentation.

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure PostgreSQL is running: `brew services list | grep postgres`
   - Check database exists: `psql -l | grep app_db`
   - Verify connection settings in `.env`

2. **Missing Google API Key**
   - Set `GOOGLE_API_KEY` in `.env` file
   - Get API key from [Google AI Studio](https://aistudio.google.com/app/apikey)

3. **MCP Server Connection Error**
   - Ensure MCP server is running on port 9000
   - Check `DEVTRON_MCP_SERVER_URL` in `.env`

4. **Model Download Issues**
   - First startup downloads ~1.3GB embedding model
   - Ensure stable internet connection
   - Check available disk space

5. **Port Already in Use**
   - Change `PORT` in `.env` file
   - Kill existing processes: `lsof -ti:8000 | xargs kill -9`

### Logs

Check application logs for detailed error information:
- Main server logs are displayed in the terminal
- MCP server logs are displayed in its terminal
- Set `LOG_LEVEL=debug` in `.env` for verbose logging

## Development

### Project Structure

```
devtron-agent/
├── server.py                 # Main FastAPI server
├── devtron_mcp/
│   └── server.py            # MCP server for Devtron API integration
├── devtron.py               # Devtron agent implementation
├── devtron_agent_config.py  # Configuration management
├── models.py                # Data models
├── tool.py                  # Custom tools for the agent
├── db/
│   └── repository.py        # Database operations
├── prompt/
│   ├── system_prompt.j2     # System prompt template
│   ├── chat_request.j2      # Chat request template
│   └── tmplate.py           # Template rendering
├── devtron-spec/            # OpenAPI specifications for Devtron
├── vector_store.py          # Vector store implementation
├── pyproject.toml           # Project dependencies
├── .env                     # Environment variables
└── README.md               # This file
```

### Adding New Features

1. **Custom Tools**: Add new tools in `tool.py`
2. **API Endpoints**: Extend `server.py` with new FastAPI routes
3. **Database Models**: Update `models.py` for new data structures
4. **Prompts**: Modify templates in `prompt/` directory

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the Devtron Enterprise License.

## Support

For issues and questions:
- Create an issue in the repository
- Contact the Devtron team
- Check the [Devtron documentation](https://docs.devtron.ai/)

---

**Note**: This agent requires valid Devtron credentials and Google API key for full functionality. For testing purposes, you can run the service without these credentials, but some features will be limited.