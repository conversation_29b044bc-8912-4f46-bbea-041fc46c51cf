from pydantic_ai import Agent
from pydantic_ai.common_tools.duckduckgo import duckduckgo_search_tool
from pydantic_ai.mcp import MCPServerHTTP
from prompt.tmplate import TemplateRenderer
import os
from dataclasses import dataclass
from loguru import logger

from tool import TOOL_FUNCTIONS
from smart_tools import SMART_TOOL_FUNCTIONS, smart_tools_instance
from model_config import get_model_config
from query_classifier import QueryClassifier
from intelligent_tool_router import IntelligentToolRouter


class DevtronAgentConfig:
    def __init__(self):
        self.devtron_agent = None
        self.smart_devtron_agent = None  # Enhanced agent with smart routing
        self.mcp_server = None
        self.AgentDep = None
        self.prompt = None
        self.smart_prompt = None
        self.classifier = QueryClassifier()
        self.router = IntelligentToolRouter()

    async def on_startup(self):
        # Get model configuration
        model_config = get_model_config()
        logger.info(f"Using AI model: {model_config.provider.value} - {model_config.model_name}")

        # Validate model configuration
        if not model_config.validate():
            raise ValueError(f"Invalid model configuration for {model_config.provider.value}")

        # AgentDep class for dependency injection
        @dataclass
        class AgentDep:
            session_id: str = None
            query_context: str = None  # Store original query for smart routing
        self.AgentDep = AgentDep

        # System prompt setup
        system_prompt_path = os.getenv("SYSTEM_PROMPT_PATH", "./prompt/system_prompt.j2")
        smart_prompt_path = os.getenv("SMART_SYSTEM_PROMPT_PATH", "./prompt/smart_system_prompt.j2")
        template_renderer = TemplateRenderer()
        prompt = template_renderer.render(template_path=system_prompt_path)
        smart_prompt = template_renderer.render(template_path=smart_prompt_path)
        self.prompt = prompt
        self.smart_prompt = smart_prompt

        # MCP server instance
        devtron_mcp_server_url=os.getenv("DEVTRON_MCP_SERVER_URL", "http://127.0.0.1:9000/mcp1/")
        self.mcp_server = MCPServerHTTP(url=devtron_mcp_server_url, timeout=35)

        # Original Devtron agent instance (for backward compatibility)
        self.devtron_agent = Agent(
            model_config.model_string,
            output_type=str,
            system_prompt=prompt,
            deps_type=AgentDep,
            mcp_servers=[self.mcp_server],
        )
        # add tools defined in tool.py
        for tool in TOOL_FUNCTIONS:
            self.devtron_agent.tool(tool)
        # Register duckduckgo_search_tool
        self.devtron_agent.tool_plain(duckduckgo_search_tool().function)

        # Enhanced Smart Devtron agent with intelligent routing
        self.smart_devtron_agent = Agent(
            model_config.model_string,
            output_type=str,
            system_prompt=smart_prompt,
            deps_type=AgentDep,
            mcp_servers=[self.mcp_server],
        )
        # Add smart tools with enhanced routing
        for tool in SMART_TOOL_FUNCTIONS:
            self.smart_devtron_agent.tool(tool)
        # Register duckduckgo_search_tool
        self.smart_devtron_agent.tool_plain(duckduckgo_search_tool().function)

_config = DevtronAgentConfig()

# function to initialize and get the agent configuration
async def get_devtron_agent_config() -> Agent:
    """Get the original agent (for backward compatibility)"""
    if _config.devtron_agent is None:
        await _config.on_startup()
    return _config.devtron_agent

async def get_smart_devtron_agent_config() -> Agent:
    """Get the enhanced smart agent with intelligent routing"""
    if _config.smart_devtron_agent is None:
        await _config.on_startup()
    return _config.smart_devtron_agent

def create_smart_agent_deps(session_id: str, query: str = None):
    """Create agent dependencies with query context for smart routing"""
    return _config.AgentDep(session_id=session_id, query_context=query)