from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from loguru import logger
from query_classifier import QueryClassifier, QueryType
from pydantic_ai.tools import RunContext


@dataclass
class ToolExecutionPlan:
    """Plan for executing tools based on query classification"""
    query_type: QueryType
    recommended_tools: List[str]
    should_use_docs: bool
    execution_strategy: str
    confidence: float


class IntelligentToolRouter:
    """Routes queries to appropriate tools based on intelligent classification"""
    
    def __init__(self):
        self.classifier = QueryClassifier()
        self.tool_usage_stats = {}  # Track tool usage for optimization
        
    def create_execution_plan(self, query: str, context: Optional[Dict] = None) -> ToolExecutionPlan:
        """
        Create an execution plan for the given query
        
        Args:
            query: User's query
            context: Additional context (session history, etc.)
            
        Returns:
            ToolExecutionPlan with recommended approach
        """
        query_type = self.classifier.classify_query(query)
        recommended_tools = self.classifier.get_tool_recommendations(query_type, query)
        should_use_docs = self.classifier.should_use_runbook(query_type)
        
        # Determine execution strategy
        strategy = self._get_execution_strategy(query_type, query)
        confidence = self._calculate_confidence(query_type, query)
        
        plan = ToolExecutionPlan(
            query_type=query_type,
            recommended_tools=recommended_tools,
            should_use_docs=should_use_docs,
            execution_strategy=strategy,
            confidence=confidence
        )
        
        logger.info(f"Created execution plan: {plan}")
        return plan
    
    def _get_execution_strategy(self, query_type: QueryType, query: str) -> str:
        """Determine the execution strategy based on query type"""
        strategies = {
            QueryType.GENERAL_KNOWLEDGE: "direct_response",
            QueryType.SPECIFIC_DEBUGGING: "sequential_debugging",
            QueryType.DEVTRON_FEATURES: "documentation_first",
            QueryType.COMPLEX_TROUBLESHOOTING: "comprehensive_analysis"
        }
        return strategies.get(query_type, "comprehensive_analysis")
    
    def _calculate_confidence(self, query_type: QueryType, query: str) -> float:
        """Calculate confidence in the classification"""
        # Simple confidence calculation based on keyword matches
        query_lower = query.lower()
        
        if query_type == QueryType.GENERAL_KNOWLEDGE:
            general_indicators = ["what is", "explain", "how does", "difference"]
            matches = sum(1 for indicator in general_indicators if indicator in query_lower)
            return min(0.9, 0.5 + (matches * 0.2))
        
        elif query_type == QueryType.SPECIFIC_DEBUGGING:
            debug_indicators = ["unhealthy", "failing", "error", "issue"]
            app_indicators = ["application_id", "app_id", "environment_id"]
            debug_matches = sum(1 for indicator in debug_indicators if indicator in query_lower)
            app_matches = sum(1 for indicator in app_indicators if indicator in query_lower)
            return min(0.95, 0.6 + (debug_matches * 0.15) + (app_matches * 0.2))
        
        elif query_type == QueryType.DEVTRON_FEATURES:
            devtron_indicators = ["devtron", "install", "helm", "pipeline"]
            matches = sum(1 for indicator in devtron_indicators if indicator in query_lower)
            return min(0.9, 0.5 + (matches * 0.2))
        
        return 0.7  # Default confidence
    
    def should_skip_documentation_search(self, query: str, query_type: QueryType) -> bool:
        """
        Determine if documentation search should be skipped for efficiency
        
        Args:
            query: User's query
            query_type: Classified query type
            
        Returns:
            True if documentation search should be skipped
        """
        # Skip documentation search for simple general knowledge questions
        if query_type == QueryType.GENERAL_KNOWLEDGE:
            simple_patterns = [
                "what is kubernetes",
                "what is a pod",
                "what is a namespace",
                "explain container",
                "difference between"
            ]
            query_lower = query.lower()
            if any(pattern in query_lower for pattern in simple_patterns):
                logger.info("Skipping documentation search for simple general knowledge")
                return True
        
        # Skip for very specific debugging queries that need live data
        if query_type == QueryType.SPECIFIC_DEBUGGING:
            specific_patterns = [
                "application_id",
                "environment_id", 
                "why is my app",
                "check logs for"
            ]
            query_lower = query.lower()
            if any(pattern in query_lower for pattern in specific_patterns):
                logger.info("Skipping documentation search for specific debugging")
                return True
        
        return False
    
    def optimize_tool_order(self, tools: List[str], query: str) -> List[str]:
        """
        Optimize the order of tool execution based on query content and efficiency
        
        Args:
            tools: List of recommended tools
            query: User's query
            
        Returns:
            Optimized tool order
        """
        if not tools:
            return tools
        
        query_lower = query.lower()
        optimized = []
        
        # Prioritize based on query content
        priority_map = {
            # High priority for health/status queries
            "get_app_detail": 10 if any(word in query_lower for word in ["health", "status", "unhealthy"]) else 5,
            "get_deployment_status": 9 if any(word in query_lower for word in ["deployment", "deploy"]) else 4,
            "get_logs": 8 if any(word in query_lower for word in ["log", "error", "crash"]) else 3,
            "get_event": 7 if any(word in query_lower for word in ["event", "warning"]) else 2,
            "get_manifest": 6 if any(word in query_lower for word in ["manifest", "config"]) else 1,
            "retrieve_devtron_info": 5  # Default priority for documentation
        }
        
        # Sort tools by priority
        sorted_tools = sorted(tools, key=lambda tool: priority_map.get(tool, 0), reverse=True)
        
        logger.info(f"Optimized tool order: {sorted_tools}")
        return sorted_tools
    
    def get_smart_system_prompt(self, query_type: QueryType, query: str) -> str:
        """
        Generate a smart system prompt based on query classification
        
        Args:
            query_type: Classified query type
            query: Original query
            
        Returns:
            Optimized system prompt
        """
        base_prompt = """You are Devtron Intelligence, a virtual assistant for the Devtron platform.
You are an expert in Kubernetes and the Devtron platform."""
        
        if query_type == QueryType.GENERAL_KNOWLEDGE:
            return base_prompt + """
            
For this query, use your built-in knowledge to provide a clear, concise answer.
Only use tools if the query specifically requires live data or Devtron-specific information.
Focus on providing accurate, helpful information from your training data."""
        
        elif query_type == QueryType.SPECIFIC_DEBUGGING:
            return base_prompt + """
            
This is a specific debugging query. Follow this approach:
1. First, get application details to understand the current state
2. Check deployment status if relevant
3. Examine logs and events for specific issues
4. Only search documentation if you need Devtron-specific troubleshooting steps
5. Provide specific, actionable solutions"""
        
        elif query_type == QueryType.DEVTRON_FEATURES:
            return base_prompt + """
            
This query is about Devtron platform features. 
1. Search the documentation for accurate, up-to-date information
2. Provide specific instructions and URLs when possible
3. Focus on practical implementation details"""
        
        else:  # COMPLEX_TROUBLESHOOTING
            return base_prompt + """
            
This is a complex troubleshooting query. Use a systematic approach:
1. Gather relevant application data first
2. Search documentation for known issues and solutions
3. Analyze the collected information comprehensively
4. Provide a structured response with root cause analysis and solutions"""
