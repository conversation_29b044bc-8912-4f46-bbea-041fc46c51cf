from enum import Enum
from typing import Dict, List, Optional
import re
from loguru import logger


class QueryType(Enum):
    """Types of queries the agent can handle"""
    GENERAL_KNOWLEDGE = "general_knowledge"  # Basic questions answerable from LLM knowledge
    SPECIFIC_DEBUGGING = "specific_debugging"  # Requires app-specific data
    DEVTRON_FEATURES = "devtron_features"  # Devtron platform information
    COMPLEX_TROUBLESHOOTING = "complex_troubleshooting"  # Multi-step debugging


class QueryClassifier:
    """Classifies user queries to determine the best response strategy"""
    
    def __init__(self):
        # Keywords that indicate specific debugging needs
        self.debugging_keywords = [
            "unhealthy", "failing", "error", "crash", "down", "not working",
            "issue", "problem", "debug", "troubleshoot", "logs", "events",
            "manifest", "deployment status", "pod", "container"
        ]
        
        # Keywords that indicate Devtron-specific feature questions
        self.devtron_feature_keywords = [
            "devtron", "install", "setup", "configure", "helm", "chart",
            "pipeline", "workflow", "deployment", "gitops", "ci/cd",
            "security", "policy", "rbac", "integration"
        ]
        
        # Keywords that indicate general Kubernetes knowledge
        self.general_k8s_keywords = [
            "what is", "explain", "how does", "difference between",
            "kubernetes", "k8s", "concept", "definition", "overview"
        ]
        
        # Patterns that indicate specific app/env references
        self.specific_app_patterns = [
            r"application[_\s]+id[_\s]*\d+",
            r"app[_\s]+id[_\s]*\d+",
            r"environment[_\s]+id[_\s]*\d+",
            r"env[_\s]+id[_\s]*\d+",
            r"namespace[_\s]+[\w-]+",
            r"pod[_\s]+[\w-]+"
        ]

    def classify_query(self, query: str) -> QueryType:
        """
        Classify a user query to determine the best response strategy
        
        Args:
            query: The user's query string
            
        Returns:
            QueryType: The classified type of query
        """
        query_lower = query.lower()
        logger.info(f"Classifying query: {query}")
        
        # Check for specific app/environment references first
        if self._has_specific_references(query_lower):
            if self._has_debugging_intent(query_lower):
                logger.info("Classified as SPECIFIC_DEBUGGING")
                return QueryType.SPECIFIC_DEBUGGING
            else:
                logger.info("Classified as COMPLEX_TROUBLESHOOTING")
                return QueryType.COMPLEX_TROUBLESHOOTING
        
        # Check for Devtron feature questions
        if self._has_devtron_features_intent(query_lower):
            logger.info("Classified as DEVTRON_FEATURES")
            return QueryType.DEVTRON_FEATURES
        
        # Check for general knowledge questions
        if self._has_general_knowledge_intent(query_lower):
            logger.info("Classified as GENERAL_KNOWLEDGE")
            return QueryType.GENERAL_KNOWLEDGE
        
        # Default to complex troubleshooting for ambiguous cases
        logger.info("Classified as COMPLEX_TROUBLESHOOTING (default)")
        return QueryType.COMPLEX_TROUBLESHOOTING

    def _has_specific_references(self, query: str) -> bool:
        """Check if query contains specific app/environment references"""
        for pattern in self.specific_app_patterns:
            if re.search(pattern, query):
                return True
        return False

    def _has_debugging_intent(self, query: str) -> bool:
        """Check if query indicates debugging intent"""
        return any(keyword in query for keyword in self.debugging_keywords)

    def _has_devtron_features_intent(self, query: str) -> bool:
        """Check if query is about Devtron features"""
        return any(keyword in query for keyword in self.devtron_feature_keywords)

    def _has_general_knowledge_intent(self, query: str) -> bool:
        """Check if query is asking for general knowledge"""
        return any(keyword in query for keyword in self.general_k8s_keywords)

    def get_tool_recommendations(self, query_type: QueryType, query: str) -> List[str]:
        """
        Get recommended tools based on query type
        
        Args:
            query_type: The classified query type
            query: Original query for context
            
        Returns:
            List of recommended tool names in priority order
        """
        recommendations = {
            QueryType.GENERAL_KNOWLEDGE: [],  # Use LLM knowledge only
            QueryType.SPECIFIC_DEBUGGING: [
                "get_app_detail", "get_deployment_status", "get_logs", "get_event"
            ],
            QueryType.DEVTRON_FEATURES: ["retrieve_devtron_info"],
            QueryType.COMPLEX_TROUBLESHOOTING: [
                "get_app_detail", "retrieve_devtron_info", "get_deployment_status",
                "get_logs", "get_event", "get_manifest"
            ]
        }
        
        return recommendations.get(query_type, [])

    def should_use_runbook(self, query_type: QueryType) -> bool:
        """Determine if the query requires runbook/documentation search"""
        return query_type in [QueryType.DEVTRON_FEATURES, QueryType.COMPLEX_TROUBLESHOOTING]
